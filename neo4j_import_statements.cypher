// 企业预警知识图谱 Neo4j 导入语句
// 基于 table.sql 中的 t_cop_information 和 t_rep_garrep 表结构

// ========================================
// 1. 创建约束和索引
// ========================================

// 企业节点约束
CREATE CONSTRAINT company_id_unique IF NOT EXISTS FOR (c:Company) REQUIRE c.cop_id IS UNIQUE;
CREATE INDEX company_name_index IF NOT EXISTS FOR (c:Company) ON (c.cop_name);
CREATE INDEX company_code_index IF NOT EXISTS FOR (c:Company) ON (c.cop_code);

// 报表节点约束
CREATE CONSTRAINT report_id_unique IF NOT EXISTS FOR (r:Report) REQUIRE r.rep_garrep_id IS UNIQUE;
CREATE INDEX report_date_index IF NOT EXISTS FOR (r:Report) ON (r.rep_date);
CREATE INDEX report_level_index IF NOT EXISTS FOR (r:Report) ON (r.level);

// 人员节点约束
CREATE CONSTRAINT person_id_unique IF NOT EXISTS FOR (p:Person) REQUIRE p.person_id IS UNIQUE;
CREATE INDEX person_name_index IF NOT EXISTS FOR (p:Person) ON (p.name);

// 地址节点约束
CREATE CONSTRAINT location_id_unique IF NOT EXISTS FOR (l:Location) REQUIRE l.location_id IS UNIQUE;

// 部门节点约束
CREATE CONSTRAINT department_id_unique IF NOT EXISTS FOR (d:Department) REQUIRE d.dept_id IS UNIQUE;

// ========================================
// 2. 导入企业节点
// ========================================

LOAD CSV WITH HEADERS FROM 'file:///companies.csv' AS row
CREATE (c:Company {
    cop_id: toInteger(row.cop_id),
    cop_name: row.cop_name,
    cop_code: row.cop_code,
    corporate_code: row.corporate_code,
    business_licence: row.business_licence,
    tax_registration: row.tax_registration,
    cop_population: toInteger(row.cop_population),
    cop_address: row.cop_address,
    cop_phone: row.cop_phone,
    cop_fax: row.cop_fax,
    office_property: toInteger(row.office_property),
    office_owner: row.office_owner,
    office_owner_phone: row.office_owner_phone,
    property_id: toInteger(row.property_id),
    org_id: toInteger(row.org_id),
    lon: toFloat(row.lon),
    lat: toFloat(row.lat),
    is_cancel: toInteger(row.is_cancel),
    scope_of_business: row.scope_of_business,
    lease_term: row.lease_term,
    is_location: toInteger(row.is_location),
    is_local: toInteger(row.is_local),
    create_time: datetime(row.create_time),
    update_time: datetime(row.update_time),
    create_by: row.create_by,
    update_by: row.update_by,
    note: row.note
});

// ========================================
// 3. 导入报表节点
// ========================================

LOAD CSV WITH HEADERS FROM 'file:///reports.csv' AS row
CREATE (r:Report {
    rep_garrep_id: toInteger(row.rep_garrep_id),
    rep_date: datetime(row.rep_date),
    level_all: row.level_all,
    level: toInteger(row.level),
    propaymonth: toFloat(row.propaymonth),
    propaypopulation: toFloat(row.propaypopulation),
    propaymoney: toFloat(row.propaymoney),
    protaxmonth: toFloat(row.protaxmonth),
    protaxmoney: toFloat(row.protaxmoney),
    proinsuremonth: toFloat(row.proinsuremonth),
    proinsuremoney: toFloat(row.proinsuremoney),
    prowatermonth: toFloat(row.prowatermonth),
    prowatermoney: toFloat(row.prowatermoney),
    proenergymonth: toFloat(row.proenergymonth),
    proenergymoney: toFloat(row.proenergymoney),
    prorentmonth: toFloat(row.prorentmonth),
    prorentmoney: toFloat(row.prorentmoney),
    problem: row.problem,
    copoversee: row.copoversee,
    copadvice: row.copadvice,
    copdeal: row.copdeal,
    copresult: row.copresult,
    copafterwork: row.copafterwork,
    cophelp: row.cophelp,
    other: row.other,
    propaymonths: row.propaymonths,
    protaxmonths: row.protaxmonths,
    proinsuremonths: row.proinsuremonths,
    prowatermonths: row.prowatermonths,
    proenergymonths: row.proenergymonths,
    prorentmonths: row.prorentmonths,
    create_time: datetime(row.create_time),
    update_time: datetime(row.update_time),
    create_by: row.create_by,
    update_by: row.update_by,
    note: row.note
});

// ========================================
// 4. 导入人员节点
// ========================================

LOAD CSV WITH HEADERS FROM 'file:///persons.csv' AS row
CREATE (p:Person {
    person_id: row.person_id,
    name: row.name,
    phone: row.phone,
    person_type: row.person_type  // legal_person, manager, contact, office_owner
});

// ========================================
// 5. 导入地址节点
// ========================================

LOAD CSV WITH HEADERS FROM 'file:///locations.csv' AS row
CREATE (l:Location {
    location_id: row.location_id,
    address: row.address,
    lon: toFloat(row.lon),
    lat: toFloat(row.lat)
});

// ========================================
// 6. 导入部门节点
// ========================================

LOAD CSV WITH HEADERS FROM 'file:///departments.csv' AS row
CREATE (d:Department {
    dept_id: toInteger(row.dept_id),
    dept_name: row.dept_name
});

// ========================================
// 7. 创建企业-报表关系
// ========================================

LOAD CSV WITH HEADERS FROM 'file:///company_reports.csv' AS row
MATCH (c:Company {cop_id: toInteger(row.cop_id)})
MATCH (r:Report {rep_garrep_id: toInteger(row.rep_garrep_id)})
CREATE (c)-[:HAS_REPORT {
    report_date: datetime(row.rep_date),
    risk_level: toInteger(row.level)
}]->(r);

// ========================================
// 8. 创建企业-人员关系
// ========================================

LOAD CSV WITH HEADERS FROM 'file:///company_persons.csv' AS row
MATCH (c:Company {cop_id: toInteger(row.cop_id)})
MATCH (p:Person {person_id: row.person_id})
CREATE (c)-[:HAS_PERSON {
    role: row.role,  // legal_person, manager, contact, office_owner
    create_time: datetime(row.create_time)
}]->(p);

// ========================================
// 9. 创建企业-地址关系
// ========================================

LOAD CSV WITH HEADERS FROM 'file:///company_locations.csv' AS row
MATCH (c:Company {cop_id: toInteger(row.cop_id)})
MATCH (l:Location {location_id: row.location_id})
CREATE (c)-[:LOCATED_AT {
    location_type: row.location_type  // office, registered
}]->(l);

// ========================================
// 10. 创建报表-部门关系
// ========================================

LOAD CSV WITH HEADERS FROM 'file:///report_departments.csv' AS row
MATCH (r:Report {rep_garrep_id: toInteger(row.rep_garrep_id)})
MATCH (d:Department {dept_id: toInteger(row.dept_id)})
CREATE (r)-[:REPORTED_BY {
    report_date: datetime(row.report_date)
}]->(d);
