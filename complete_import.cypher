// 企业-法人关系完整导入语句
// 确保法人节点显示姓名而不是电话号码

// ========================================
// 1. 清理现有数据（可选，如果需要重新导入）
// ========================================
// MATCH (c:Company)-[r:HAS_LEGAL_PERSON]->(p:LegalPerson) DELETE r, c, p;

// ========================================
// 2. 删除旧约束（如果存在）
// ========================================
DROP CONSTRAINT legal_person_name_unique IF EXISTS;
DROP CONSTRAINT company_id_unique IF EXISTS;

// ========================================
// 3. 创建新约束
// ========================================
CREATE CONSTRAINT company_id_unique IF NOT EXISTS FOR (c:Company) REQUIRE c.cop_id IS UNIQUE;
CREATE CONSTRAINT legal_person_name_unique IF NOT EXISTS FOR (p:LegalPerson) REQUIRE p.name IS UNIQUE;

// ========================================
// 4. 导入企业和法人数据
// ========================================
LOAD CSV WITH HEADERS FROM "file:///company.csv" AS line
WITH line WHERE line.cop_owner IS NOT NULL AND trim(line.cop_owner) <> ""
MERGE (c:Company {cop_id: toInteger(line.cop_id)})
SET c.cop_name = line.cop_name,
    c.cop_code = line.cop_code,
    c.cop_owner = line.cop_owner,
    c.corporate_code = line.corporate_code,
    c.business_licence = line.business_licence,
    c.tax_registration = line.tax_registration,
    c.cop_population = CASE WHEN line.cop_population IS NOT NULL THEN toInteger(line.cop_population) ELSE 0 END,
    c.cop_address = line.cop_address,
    c.cop_phone = line.cop_phone,
    c.cop_fax = line.cop_fax,
    c.scope_of_business = line.scope_of_business,
    c.is_cancel = CASE WHEN line.is_cancel IS NOT NULL THEN toInteger(line.is_cancel) ELSE 0 END,
    c.create_time = CASE WHEN line.create_time IS NOT NULL THEN datetime(line.create_time) ELSE null END,
    c.update_time = CASE WHEN line.update_time IS NOT NULL THEN datetime(line.update_time) ELSE null END
WITH line, c
MERGE (p:LegalPerson {name: line.cop_owner})
SET p.phone = line.cop_phone,
    p.cop_owner = line.cop_owner
WITH line, c, p
MERGE (c)-[:HAS_LEGAL_PERSON {
    create_time: CASE WHEN line.create_time IS NOT NULL THEN datetime(line.create_time) ELSE datetime() END
}]->(p);

// ========================================
// 5. 验证导入结果
// ========================================

// 检查节点数量
MATCH (c:Company) 
WITH count(c) as company_count
MATCH (p:LegalPerson) 
WITH company_count, count(p) as legal_person_count
MATCH ()-[r:HAS_LEGAL_PERSON]->() 
RETURN company_count, legal_person_count, count(r) as relation_count;

// 检查数据示例
MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson) 
RETURN c.cop_name as 企业名称, p.name as 法人姓名, p.phone as 联系电话, c.cop_address as 企业地址
LIMIT 10;

// 检查法人名下多个企业的情况
MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson)
WITH p, collect(c.cop_name) as companies
WHERE size(companies) > 1
RETURN p.name as 法人姓名, companies as 名下企业, size(companies) as 企业数量
ORDER BY 企业数量 DESC;

// 检查数据完整性
MATCH (c:Company) 
WHERE NOT (c)-[:HAS_LEGAL_PERSON]->() 
RETURN count(c) as 没有法人的企业数量;

// ========================================
// 6. 创建索引以提高查询性能
// ========================================
CREATE INDEX company_name_index IF NOT EXISTS FOR (c:Company) ON (c.cop_name);
CREATE INDEX company_code_index IF NOT EXISTS FOR (c:Company) ON (c.cop_code);
CREATE INDEX legal_person_phone_index IF NOT EXISTS FOR (p:LegalPerson) ON (p.phone);

// ========================================
// 7. 常用查询示例
// ========================================

// 查找特定企业的法人
// MATCH (c:Company {cop_name: "您的企业名称"})-[:HAS_LEGAL_PERSON]->(p:LegalPerson)
// RETURN c.cop_name, p.name, p.phone;

// 查找特定法人的所有企业
// MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson {name: "法人姓名"})
// RETURN p.name, collect(c.cop_name) as companies;

// 统计法人企业数量排行
// MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson)
// WITH p, count(c) as company_count
// RETURN p.name, company_count
// ORDER BY company_count DESC
// LIMIT 20;
