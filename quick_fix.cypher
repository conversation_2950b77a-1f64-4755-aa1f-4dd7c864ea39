-- 快速修正版本 - 直接可用

-- 1. 先创建约束
CREATE CONSTRAINT company_id_unique IF NOT EXISTS FOR (c:Company) REQUIRE c.cop_id IS UNIQUE;
CREATE CONSTRAINT legal_person_name_unique IF NOT EXISTS FOR (p:Legal<PERSON>erson) REQUIRE p.cop_owner IS UNIQUE;

-- 2. 修正后的导入语句
LOAD CSV WITH HEADERS FROM "file:///company.csv" AS line
MERGE (c:Company {cop_id: toInteger(line.cop_id)})
SET c.cop_name = line.cop_name,
    c.cop_owner = line.cop_owner
WITH line, c
WHERE line.cop_owner IS NOT NULL AND trim(line.cop_owner) <> ""
MERGE (p:LegalPerson {cop_owner: line.cop_owner})
SET p.phone = line.cop_phone
WITH line, c, p
MERGE (c)-[:HAS_LEGAL_PERSON]->(p);
