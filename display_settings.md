# Neo4j 节点显示设置指南

## 问题：法人节点显示电话号码而不是姓名

### 解决方案1：在Neo4j Browser中设置显示属性

1. **打开Neo4j Browser**
2. **执行查询显示节点**：
   ```cypher
   MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson) 
   RETURN c, p 
   LIMIT 20
   ```

3. **设置显示属性**：
   - 在结果图形视图中，点击左侧的 **LegalPerson** 节点类型
   - 在弹出的设置面板中，找到 **Caption** 选项
   - 将 Caption 从 `phone` 改为 `cop_owner`
   - 点击应用

### 解决方案2：修改数据模型（推荐）

将法人节点的主要属性改为更直观的名称：

```cypher
-- 1. 创建约束（使用name作为主属性）
CREATE CONSTRAINT legal_person_name_unique IF NOT EXISTS FOR (p:LegalPerson) REQUIRE p.name IS UNIQUE;

-- 2. 修正后的导入语句
LOAD CSV WITH HEADERS FROM "file:///company.csv" AS line
WITH line WHERE line.cop_owner IS NOT NULL AND trim(line.cop_owner) <> ""
MERGE (c:Company {cop_id: toInteger(line.cop_id)})
SET c.cop_name = line.cop_name,
    c.cop_owner = line.cop_owner,
    c.cop_code = line.cop_code,
    c.cop_address = line.cop_address,
    c.cop_phone = line.cop_phone
WITH line, c
MERGE (p:LegalPerson {name: line.cop_owner})  -- 使用name而不是cop_owner
SET p.phone = line.cop_phone,
    p.cop_owner = line.cop_owner  -- 保留原字段作为备份
WITH line, c, p
MERGE (c)-[:HAS_LEGAL_PERSON]->(p);
```

### 解决方案3：如果已经导入数据，更新现有节点

```cypher
-- 为现有的LegalPerson节点添加name属性
MATCH (p:LegalPerson)
SET p.name = p.cop_owner;

-- 验证更新
MATCH (p:LegalPerson) 
RETURN p.name, p.cop_owner, p.phone 
LIMIT 10;
```

### 解决方案4：使用查询时指定显示

```cypher
-- 查询时明确指定要显示的属性
MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson) 
RETURN c.cop_name as 企业名称, p.cop_owner as 法人姓名, p.phone as 联系电话
LIMIT 20;
```

### 推荐的完整导入脚本

```cypher
-- 删除旧约束（如果存在）
DROP CONSTRAINT legal_person_name_unique IF EXISTS;

-- 创建新约束
CREATE CONSTRAINT legal_person_name_unique IF NOT EXISTS FOR (p:LegalPerson) REQUIRE p.name IS UNIQUE;
CREATE CONSTRAINT company_id_unique IF NOT EXISTS FOR (c:Company) REQUIRE c.cop_id IS UNIQUE;

-- 清空现有数据（可选）
MATCH (c:Company)-[r:HAS_LEGAL_PERSON]->(p:LegalPerson) DELETE r, c, p;

-- 重新导入数据
LOAD CSV WITH HEADERS FROM "file:///company.csv" AS line
WITH line WHERE line.cop_owner IS NOT NULL AND trim(line.cop_owner) <> ""
MERGE (c:Company {cop_id: toInteger(line.cop_id)})
SET c.cop_name = line.cop_name,
    c.cop_owner = line.cop_owner,
    c.cop_code = line.cop_code,
    c.cop_address = line.cop_address,
    c.cop_phone = line.cop_phone
WITH line, c
MERGE (p:LegalPerson {name: line.cop_owner})
SET p.phone = line.cop_phone
WITH line, c, p
MERGE (c)-[:HAS_LEGAL_PERSON]->(p);

-- 验证结果
MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson) 
RETURN c.cop_name, p.name, p.phone 
LIMIT 10;
```

### Neo4j Browser 显示设置步骤

1. 执行查询后，在图形视图中
2. 点击左侧面板的节点类型（如 LegalPerson）
3. 在 **Caption** 下拉菜单中选择 `name` 或 `cop_owner`
4. 在 **Size** 中可以选择节点大小依据
5. 在 **Color** 中可以设置节点颜色

这样法人节点就会显示姓名而不是电话号码了！
