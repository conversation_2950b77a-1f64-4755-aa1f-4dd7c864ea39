-- 企业预警知识图谱数据准备脚本 (优化版本)
-- 基于您提供的查询逻辑，只包含有报表记录且未注销的企业

-- ========================================
-- 基础查询：获取有效企业列表
-- ========================================
-- 这个查询对应您提供的逻辑：只包含有报表记录且未注销的企业
WITH valid_companies AS (
    SELECT cop_id, cop_owner
    FROM t_cop_information
    WHERE del_flag = '0' 
      AND is_cancel = '0'  
      AND cop_id IN (
          SELECT DISTINCT cop_id 
          FROM t_rep_garrep 
          WHERE del_flag = '0'
      )
)

-- ========================================
-- 1. 生成企业节点数据 (companies.csv)
-- ========================================
COPY (
    SELECT 
        c.cop_id,
        c.cop_name,
        c.cop_code,
        c.cop_owner,
        c.cop_incharge,
        c.corporate_code,
        c.business_licence,
        c.tax_registration,
        COALESCE(c.cop_population, 0) as cop_population,
        c.cop_address,
        c.cop_phone,
        c.cop_fax,
        COALESCE(c.office_property, 0) as office_property,
        c.office_owner,
        c.office_owner_phone,
        c.property_id,
        c.org_id,
        CASE WHEN c.lon ~ '^[0-9]+\.?[0-9]*$' THEN c.lon ELSE NULL END as lon,
        CASE WHEN c.lat ~ '^[0-9]+\.?[0-9]*$' THEN c.lat ELSE NULL END as lat,
        COALESCE(c.is_cancel, 0) as is_cancel,
        c.scope_of_business,
        c.cop_contacts,
        c.lease_term,
        COALESCE(c.is_location, 0) as is_location,
        COALESCE(c.is_local, 1) as is_local,
        c.create_time,
        c.update_time,
        c.create_by,
        c.update_by,
        c.note
    FROM t_cop_information c
    WHERE c.del_flag = '0' 
      AND c.is_cancel = '0'
      AND c.cop_id IN (
          SELECT DISTINCT cop_id 
          FROM t_rep_garrep 
          WHERE del_flag = '0'
      )
    ORDER BY c.cop_owner
) TO '/path/to/csv/companies.csv' WITH CSV HEADER;

-- ========================================
-- 2. 生成报表节点数据 (reports.csv)
-- ========================================
COPY (
    SELECT 
        r.rep_garrep_id,
        r.rep_date,
        r.level_all,
        COALESCE(r.level, 0) as level,
        COALESCE(r.propaymonth, 0) as propaymonth,
        COALESCE(r.propaypopulation, 0) as propaypopulation,
        COALESCE(r.propaymoney, 0) as propaymoney,
        COALESCE(r.protaxmonth, 0) as protaxmonth,
        COALESCE(r.protaxmoney, 0) as protaxmoney,
        COALESCE(r.proinsuremonth, 0) as proinsuremonth,
        COALESCE(r.proinsuremoney, 0) as proinsuremoney,
        COALESCE(r.prowatermonth, 0) as prowatermonth,
        COALESCE(r.prowatermoney, 0) as prowatermoney,
        COALESCE(r.proenergymonth, 0) as proenergymonth,
        COALESCE(r.proenergymoney, 0) as proenergymoney,
        COALESCE(r.prorentmonth, 0) as prorentmonth,
        COALESCE(r.prorentmoney, 0) as prorentmoney,
        r.problem,
        r.copoversee,
        r.copadvice,
        r.copdeal,
        r.copresult,
        r.copafterwork,
        r.cophelp,
        r.other,
        r.propaymonths,
        r.protaxmonths,
        r.proinsuremonths,
        r.prowatermonths,
        r.proenergymonths,
        r.prorentmonths,
        r.create_time,
        r.update_time,
        r.create_by,
        r.update_by,
        r.note
    FROM t_rep_garrep r
    INNER JOIN t_cop_information c ON r.cop_id = c.cop_id
    WHERE r.del_flag = '0' 
      AND c.del_flag = '0' 
      AND c.is_cancel = '0'
    ORDER BY r.cop_id, r.rep_date
) TO '/path/to/csv/reports.csv' WITH CSV HEADER;

-- ========================================
-- 3. 生成人员节点数据 (persons.csv)
-- ========================================
COPY (
    WITH person_data AS (
        -- 企业法人
        SELECT DISTINCT
            'LP_' || c.cop_id as person_id,
            c.cop_owner as name,
            c.cop_phone as phone,
            'legal_person' as person_type,
            c.cop_id
        FROM t_cop_information c
        WHERE c.cop_owner IS NOT NULL 
          AND TRIM(c.cop_owner) != '' 
          AND c.del_flag = '0' 
          AND c.is_cancel = '0'
          AND c.cop_id IN (
              SELECT DISTINCT cop_id 
              FROM t_rep_garrep 
              WHERE del_flag = '0'
          )
        
        UNION ALL
        
        -- 企业负责人
        SELECT DISTINCT
            'MG_' || c.cop_id as person_id,
            c.cop_incharge as name,
            c.cop_phone as phone,
            'manager' as person_type,
            c.cop_id
        FROM t_cop_information c
        WHERE c.cop_incharge IS NOT NULL 
          AND TRIM(c.cop_incharge) != '' 
          AND c.cop_incharge != c.cop_owner  -- 避免重复
          AND c.del_flag = '0' 
          AND c.is_cancel = '0'
          AND c.cop_id IN (
              SELECT DISTINCT cop_id 
              FROM t_rep_garrep 
              WHERE del_flag = '0'
          )
        
        UNION ALL
        
        -- 企业联系人
        SELECT DISTINCT
            'CT_' || c.cop_id as person_id,
            c.cop_contacts as name,
            c.cop_phone as phone,
            'contact' as person_type,
            c.cop_id
        FROM t_cop_information c
        WHERE c.cop_contacts IS NOT NULL 
          AND TRIM(c.cop_contacts) != '' 
          AND c.cop_contacts != c.cop_owner 
          AND c.cop_contacts != c.cop_incharge  -- 避免重复
          AND c.del_flag = '0' 
          AND c.is_cancel = '0'
          AND c.cop_id IN (
              SELECT DISTINCT cop_id 
              FROM t_rep_garrep 
              WHERE del_flag = '0'
          )
        
        UNION ALL
        
        -- 厂房业主
        SELECT DISTINCT
            'OW_' || c.cop_id as person_id,
            c.office_owner as name,
            c.office_owner_phone as phone,
            'office_owner' as person_type,
            c.cop_id
        FROM t_cop_information c
        WHERE c.office_owner IS NOT NULL 
          AND TRIM(c.office_owner) != '' 
          AND c.office_owner != c.cop_owner  -- 避免重复
          AND c.del_flag = '0' 
          AND c.is_cancel = '0'
          AND c.cop_id IN (
              SELECT DISTINCT cop_id 
              FROM t_rep_garrep 
              WHERE del_flag = '0'
          )
    )
    SELECT DISTINCT person_id, name, phone, person_type
    FROM person_data
    ORDER BY name, person_type
) TO '/path/to/csv/persons.csv' WITH CSV HEADER;

-- ========================================
-- 4. 生成地址节点数据 (locations.csv)
-- ========================================
COPY (
    SELECT DISTINCT
        'LOC_' || c.cop_id as location_id,
        c.cop_address as address,
        CASE 
            WHEN c.lon ~ '^[0-9]+\.?[0-9]*$' AND c.lon != '' 
            THEN c.lon::numeric 
            ELSE NULL 
        END as lon,
        CASE 
            WHEN c.lat ~ '^[0-9]+\.?[0-9]*$' AND c.lat != '' 
            THEN c.lat::numeric 
            ELSE NULL 
        END as lat
    FROM t_cop_information c
    WHERE c.cop_address IS NOT NULL 
      AND TRIM(c.cop_address) != '' 
      AND c.del_flag = '0' 
      AND c.is_cancel = '0'
      AND c.cop_id IN (
          SELECT DISTINCT cop_id 
          FROM t_rep_garrep 
          WHERE del_flag = '0'
      )
    ORDER BY location_id
) TO '/path/to/csv/locations.csv' WITH CSV HEADER;

-- ========================================
-- 5. 生成部门节点数据 (departments.csv)
-- ========================================
COPY (
    SELECT DISTINCT
        r.dept_id,
        COALESCE('部门_' || r.dept_id, '未知部门') as dept_name
    FROM t_rep_garrep r
    INNER JOIN t_cop_information c ON r.cop_id = c.cop_id
    WHERE r.dept_id IS NOT NULL
      AND r.del_flag = '0'
      AND c.del_flag = '0'
      AND c.is_cancel = '0'
    ORDER BY r.dept_id
) TO '/path/to/csv/departments.csv' WITH CSV HEADER;

-- ========================================
-- 6. 生成企业-报表关系数据 (company_reports.csv)
-- ========================================
COPY (
    SELECT
        r.cop_id,
        r.rep_garrep_id,
        r.rep_date,
        COALESCE(r.level, 0) as level
    FROM t_rep_garrep r
    INNER JOIN t_cop_information c ON r.cop_id = c.cop_id
    WHERE r.del_flag = '0'
      AND c.del_flag = '0'
      AND c.is_cancel = '0'
    ORDER BY r.cop_id, r.rep_date
) TO '/path/to/csv/company_reports.csv' WITH CSV HEADER;

-- ========================================
-- 7. 生成企业-人员关系数据 (company_persons.csv)
-- ========================================
COPY (
    WITH company_person_relations AS (
        -- 企业法人关系
        SELECT
            c.cop_id,
            'LP_' || c.cop_id as person_id,
            'legal_person' as role,
            c.create_time
        FROM t_cop_information c
        WHERE c.cop_owner IS NOT NULL
          AND TRIM(c.cop_owner) != ''
          AND c.del_flag = '0'
          AND c.is_cancel = '0'
          AND c.cop_id IN (
              SELECT DISTINCT cop_id
              FROM t_rep_garrep
              WHERE del_flag = '0'
          )

        UNION ALL

        -- 企业负责人关系
        SELECT
            c.cop_id,
            'MG_' || c.cop_id as person_id,
            'manager' as role,
            c.create_time
        FROM t_cop_information c
        WHERE c.cop_incharge IS NOT NULL
          AND TRIM(c.cop_incharge) != ''
          AND c.cop_incharge != c.cop_owner
          AND c.del_flag = '0'
          AND c.is_cancel = '0'
          AND c.cop_id IN (
              SELECT DISTINCT cop_id
              FROM t_rep_garrep
              WHERE del_flag = '0'
          )

        UNION ALL

        -- 企业联系人关系
        SELECT
            c.cop_id,
            'CT_' || c.cop_id as person_id,
            'contact' as role,
            c.create_time
        FROM t_cop_information c
        WHERE c.cop_contacts IS NOT NULL
          AND TRIM(c.cop_contacts) != ''
          AND c.cop_contacts != c.cop_owner
          AND c.cop_contacts != c.cop_incharge
          AND c.del_flag = '0'
          AND c.is_cancel = '0'
          AND c.cop_id IN (
              SELECT DISTINCT cop_id
              FROM t_rep_garrep
              WHERE del_flag = '0'
          )

        UNION ALL

        -- 厂房业主关系
        SELECT
            c.cop_id,
            'OW_' || c.cop_id as person_id,
            'office_owner' as role,
            c.create_time
        FROM t_cop_information c
        WHERE c.office_owner IS NOT NULL
          AND TRIM(c.office_owner) != ''
          AND c.office_owner != c.cop_owner
          AND c.del_flag = '0'
          AND c.is_cancel = '0'
          AND c.cop_id IN (
              SELECT DISTINCT cop_id
              FROM t_rep_garrep
              WHERE del_flag = '0'
          )
    )
    SELECT cop_id, person_id, role, create_time
    FROM company_person_relations
    ORDER BY cop_id, role
) TO '/path/to/csv/company_persons.csv' WITH CSV HEADER;

-- ========================================
-- 8. 生成企业-地址关系数据 (company_locations.csv)
-- ========================================
COPY (
    SELECT
        c.cop_id,
        'LOC_' || c.cop_id as location_id,
        'office' as location_type
    FROM t_cop_information c
    WHERE c.cop_address IS NOT NULL
      AND TRIM(c.cop_address) != ''
      AND c.del_flag = '0'
      AND c.is_cancel = '0'
      AND c.cop_id IN (
          SELECT DISTINCT cop_id
          FROM t_rep_garrep
          WHERE del_flag = '0'
      )
    ORDER BY c.cop_id
) TO '/path/to/csv/company_locations.csv' WITH CSV HEADER;

-- ========================================
-- 9. 生成报表-部门关系数据 (report_departments.csv)
-- ========================================
COPY (
    SELECT
        r.rep_garrep_id,
        r.dept_id,
        r.rep_date as report_date
    FROM t_rep_garrep r
    INNER JOIN t_cop_information c ON r.cop_id = c.cop_id
    WHERE r.dept_id IS NOT NULL
      AND r.del_flag = '0'
      AND c.del_flag = '0'
      AND c.is_cancel = '0'
    ORDER BY r.rep_garrep_id
) TO '/path/to/csv/report_departments.csv' WITH CSV HEADER;
