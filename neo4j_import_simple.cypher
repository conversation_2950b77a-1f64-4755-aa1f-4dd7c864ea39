-- 企业和法人关系的简化 Neo4j 导入语句

-- ========================================
-- 1. 创建约束和索引
-- ========================================

-- 企业节点约束
CREATE CONSTRAINT company_id_unique IF NOT EXISTS FOR (c:Company) REQUIRE c.cop_id IS UNIQUE;
CREATE INDEX company_name_index IF NOT EXISTS FOR (c:Company) ON (c.cop_name);
CREATE INDEX company_code_index IF NOT EXISTS FOR (c:Company) ON (c.cop_code);

-- 法人节点约束
CREATE CONSTRAINT legal_person_name_unique IF NOT EXISTS FOR (p:LegalPerson) REQUIRE p.person_name IS UNIQUE;
CREATE INDEX legal_person_name_index IF NOT EXISTS FOR (p:LegalPerson) ON (p.person_name);

-- ========================================
-- 2. 导入企业节点
-- ========================================

LOAD CSV WITH HEADERS FROM 'file:///companies.csv' AS row
CREATE (c:Company {
    cop_id: toInteger(row.cop_id),
    cop_name: row.cop_name,
    cop_code: row.cop_code,
    cop_owner: row.cop_owner,
    corporate_code: row.corporate_code,
    business_licence: row.business_licence,
    tax_registration: row.tax_registration,
    cop_population: toInteger(row.cop_population),
    cop_address: row.cop_address,
    cop_phone: row.cop_phone,
    scope_of_business: row.scope_of_business,
    is_cancel: toInteger(row.is_cancel),
    create_time: datetime(row.create_time),
    update_time: datetime(row.update_time)
});

-- ========================================
-- 3. 导入法人节点
-- ========================================

LOAD CSV WITH HEADERS FROM 'file:///legal_persons.csv' AS row
CREATE (p:LegalPerson {
    person_name: row.person_name,
    phone: row.phone
});

-- ========================================
-- 4. 创建企业-法人关系
-- ========================================

LOAD CSV WITH HEADERS FROM 'file:///company_legal_person_relations.csv' AS row
MATCH (c:Company {cop_id: toInteger(row.cop_id)})
MATCH (p:LegalPerson {person_name: row.person_name})
CREATE (c)-[:HAS_LEGAL_PERSON {
    relation_create_time: datetime(row.relation_create_time)
}]->(p);

-- ========================================
-- 5. 验证导入结果
-- ========================================

// 检查节点数量
MATCH (c:Company) RETURN 'Company' as NodeType, count(c) as Count
UNION ALL
MATCH (p:LegalPerson) RETURN 'LegalPerson' as NodeType, count(p) as Count;

// 检查关系数量
MATCH (c:Company)-[r:HAS_LEGAL_PERSON]->(p:LegalPerson) 
RETURN 'HAS_LEGAL_PERSON' as RelationType, count(r) as Count;

// 检查数据示例
MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson) 
RETURN c.cop_name, p.person_name, c.cop_phone 
LIMIT 10;
