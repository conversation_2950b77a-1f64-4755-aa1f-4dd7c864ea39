-- 企业预警知识图谱数据准备脚本
-- 用于从原始表生成 Neo4j 导入所需的 CSV 文件

-- ========================================
-- 1. 生成企业节点数据 (companies.csv)
-- ========================================
COPY (
    SELECT 
        cop_id,
        cop_name,
        cop_code,
        corporate_code,
        business_licence,
        tax_registration,
        COALESCE(cop_population, 0) as cop_population,
        cop_address,
        cop_phone,
        cop_fax,
        COALESCE(office_property, 0) as office_property,
        office_owner,
        office_owner_phone,
        property_id,
        org_id,
        lon,
        lat,
        COALESCE(is_cancel, 0) as is_cancel,
        scope_of_business,
        lease_term,
        COALESCE(is_location, 0) as is_location,
        COALESCE(is_local, 1) as is_local,
        create_time,
        update_time,
        create_by,
        update_by,
        note
    FROM t_cop_information
    WHERE del_flag = '0'
) TO '/path/to/csv/companies.csv' WITH CSV HEADER;

-- ========================================
-- 2. 生成报表节点数据 (reports.csv)
-- ========================================
COPY (
    SELECT 
        rep_garrep_id,
        rep_date,
        level_all,
        COALESCE(level, 0) as level,
        propaymonth,
        propaypopulation,
        propaymoney,
        protaxmonth,
        protaxmoney,
        proinsuremonth,
        proinsuremoney,
        prowatermonth,
        prowatermoney,
        proenergymonth,
        proenergymoney,
        prorentmonth,
        prorentmoney,
        problem,
        copoversee,
        copadvice,
        copdeal,
        copresult,
        copafterwork,
        cophelp,
        other,
        propaymonths,
        protaxmonths,
        proinsuremonths,
        prowatermonths,
        proenergymonths,
        prorentmonths,
        create_time,
        update_time,
        create_by,
        update_by,
        note
    FROM t_rep_garrep
    WHERE del_flag = '0'
) TO '/path/to/csv/reports.csv' WITH CSV HEADER;

-- ========================================
-- 3. 生成人员节点数据 (persons.csv)
-- ========================================
COPY (
    WITH person_data AS (
        -- 企业法人
        SELECT DISTINCT
            'LP_' || cop_id as person_id,
            cop_owner as name,
            cop_phone as phone,
            'legal_person' as person_type
        FROM t_cop_information 
        WHERE cop_owner IS NOT NULL AND cop_owner != '' AND del_flag = '0'
        
        UNION ALL
        
        -- 企业负责人
        SELECT DISTINCT
            'MG_' || cop_id as person_id,
            cop_incharge as name,
            cop_phone as phone,
            'manager' as person_type
        FROM t_cop_information 
        WHERE cop_incharge IS NOT NULL AND cop_incharge != '' AND del_flag = '0'
        
        UNION ALL
        
        -- 企业联系人
        SELECT DISTINCT
            'CT_' || cop_id as person_id,
            cop_contacts as name,
            cop_phone as phone,
            'contact' as person_type
        FROM t_cop_information 
        WHERE cop_contacts IS NOT NULL AND cop_contacts != '' AND del_flag = '0'
        
        UNION ALL
        
        -- 厂房业主
        SELECT DISTINCT
            'OW_' || cop_id as person_id,
            office_owner as name,
            office_owner_phone as phone,
            'office_owner' as person_type
        FROM t_cop_information 
        WHERE office_owner IS NOT NULL AND office_owner != '' AND del_flag = '0'
    )
    SELECT DISTINCT person_id, name, phone, person_type
    FROM person_data
    ORDER BY person_id
) TO '/path/to/csv/persons.csv' WITH CSV HEADER;

-- ========================================
-- 4. 生成地址节点数据 (locations.csv)
-- ========================================
COPY (
    SELECT DISTINCT
        'LOC_' || cop_id as location_id,
        cop_address as address,
        CASE WHEN lon ~ '^[0-9]+\.?[0-9]*$' THEN lon::numeric ELSE NULL END as lon,
        CASE WHEN lat ~ '^[0-9]+\.?[0-9]*$' THEN lat::numeric ELSE NULL END as lat
    FROM t_cop_information 
    WHERE cop_address IS NOT NULL AND cop_address != '' AND del_flag = '0'
    ORDER BY location_id
) TO '/path/to/csv/locations.csv' WITH CSV HEADER;

-- ========================================
-- 5. 生成部门节点数据 (departments.csv)
-- ========================================
COPY (
    SELECT DISTINCT
        dept_id,
        '部门_' || dept_id as dept_name  -- 这里需要根据实际情况映射部门名称
    FROM t_rep_garrep 
    WHERE dept_id IS NOT NULL AND del_flag = '0'
    ORDER BY dept_id
) TO '/path/to/csv/departments.csv' WITH CSV HEADER;

-- ========================================
-- 6. 生成企业-报表关系数据 (company_reports.csv)
-- ========================================
COPY (
    SELECT 
        r.cop_id,
        r.rep_garrep_id,
        r.rep_date,
        COALESCE(r.level, 0) as level
    FROM t_rep_garrep r
    INNER JOIN t_cop_information c ON r.cop_id = c.cop_id
    WHERE r.del_flag = '0' AND c.del_flag = '0'
    ORDER BY r.cop_id, r.rep_date
) TO '/path/to/csv/company_reports.csv' WITH CSV HEADER;

-- ========================================
-- 7. 生成企业-人员关系数据 (company_persons.csv)
-- ========================================
COPY (
    WITH company_person_relations AS (
        -- 企业法人关系
        SELECT 
            cop_id,
            'LP_' || cop_id as person_id,
            'legal_person' as role,
            create_time
        FROM t_cop_information 
        WHERE cop_owner IS NOT NULL AND cop_owner != '' AND del_flag = '0'
        
        UNION ALL
        
        -- 企业负责人关系
        SELECT 
            cop_id,
            'MG_' || cop_id as person_id,
            'manager' as role,
            create_time
        FROM t_cop_information 
        WHERE cop_incharge IS NOT NULL AND cop_incharge != '' AND del_flag = '0'
        
        UNION ALL
        
        -- 企业联系人关系
        SELECT 
            cop_id,
            'CT_' || cop_id as person_id,
            'contact' as role,
            create_time
        FROM t_cop_information 
        WHERE cop_contacts IS NOT NULL AND cop_contacts != '' AND del_flag = '0'
        
        UNION ALL
        
        -- 厂房业主关系
        SELECT 
            cop_id,
            'OW_' || cop_id as person_id,
            'office_owner' as role,
            create_time
        FROM t_cop_information 
        WHERE office_owner IS NOT NULL AND office_owner != '' AND del_flag = '0'
    )
    SELECT cop_id, person_id, role, create_time
    FROM company_person_relations
    ORDER BY cop_id, role
) TO '/path/to/csv/company_persons.csv' WITH CSV HEADER;

-- ========================================
-- 8. 生成企业-地址关系数据 (company_locations.csv)
-- ========================================
COPY (
    SELECT 
        cop_id,
        'LOC_' || cop_id as location_id,
        'office' as location_type
    FROM t_cop_information 
    WHERE cop_address IS NOT NULL AND cop_address != '' AND del_flag = '0'
    ORDER BY cop_id
) TO '/path/to/csv/company_locations.csv' WITH CSV HEADER;

-- ========================================
-- 9. 生成报表-部门关系数据 (report_departments.csv)
-- ========================================
COPY (
    SELECT 
        rep_garrep_id,
        dept_id,
        rep_date as report_date
    FROM t_rep_garrep 
    WHERE dept_id IS NOT NULL AND del_flag = '0'
    ORDER BY rep_garrep_id
) TO '/path/to/csv/report_departments.csv' WITH CSV HEADER;
