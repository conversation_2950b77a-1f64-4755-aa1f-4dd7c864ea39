-- 企业和法人关系的简化导入脚本
-- 基于您的查询逻辑：只包含有报表记录且未注销的企业

-- ========================================
-- 1. 生成企业节点数据 (companies.csv)
-- ========================================
COPY (
    SELECT 
        c.cop_id,
        c.cop_name,
        c.cop_code,
        c.cop_owner,
        c.corporate_code,
        c.business_licence,
        c.tax_registration,
        COALESCE(c.cop_population, 0) as cop_population,
        c.cop_address,
        c.cop_phone,
        c.scope_of_business,
        COALESCE(c.is_cancel, 0) as is_cancel,
        c.create_time,
        c.update_time
    FROM t_cop_information c
    WHERE c.del_flag = '0' 
      AND c.is_cancel = '0'
      AND c.cop_id IN (
          SELECT DISTINCT cop_id 
          FROM t_rep_garrep 
          WHERE del_flag = '0'
      )
    ORDER BY c.cop_owner
) TO '/path/to/csv/companies.csv' WITH CSV HEADER;

-- ========================================
-- 2. 生成法人节点数据 (legal_persons.csv)
-- ========================================
COPY (
    SELECT DISTINCT
        c.cop_owner as person_name,
        c.cop_phone as phone
    FROM t_cop_information c
    WHERE c.cop_owner IS NOT NULL 
      AND TRIM(c.cop_owner) != '' 
      AND c.del_flag = '0' 
      AND c.is_cancel = '0'
      AND c.cop_id IN (
          SELECT DISTINCT cop_id 
          FROM t_rep_garrep 
          WHERE del_flag = '0'
      )
    ORDER BY c.cop_owner
) TO '/path/to/csv/legal_persons.csv' WITH CSV HEADER;

-- ========================================
-- 3. 生成企业-法人关系数据 (company_legal_person_relations.csv)
-- ========================================
COPY (
    SELECT 
        c.cop_id,
        c.cop_owner as person_name,
        c.create_time as relation_create_time
    FROM t_cop_information c
    WHERE c.cop_owner IS NOT NULL 
      AND TRIM(c.cop_owner) != '' 
      AND c.del_flag = '0' 
      AND c.is_cancel = '0'
      AND c.cop_id IN (
          SELECT DISTINCT cop_id 
          FROM t_rep_garrep 
          WHERE del_flag = '0'
      )
    ORDER BY c.cop_id
) TO '/path/to/csv/company_legal_person_relations.csv' WITH CSV HEADER;
