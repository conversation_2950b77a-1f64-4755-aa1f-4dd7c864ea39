# 企业预警知识图谱 Neo4j 导入指南

基于 `table.sql` 中的企业信息表和汇总报表表，本项目提供了完整的 Neo4j 知识图谱导入解决方案。

## 📊 数据模型设计

### 实体类型
- **Company (企业)**: 企业基本信息
- **Report (报表)**: 企业风险评估报表
- **Person (人员)**: 企业相关人员（法人、负责人、联系人、业主）
- **Location (地址)**: 企业地址信息
- **Department (部门)**: 上报部门

### 关系类型
- **HAS_REPORT**: 企业 → 报表
- **HAS_PERSON**: 企业 → 人员
- **LOCATED_AT**: 企业 → 地址
- **REPORTED_BY**: 报表 → 部门

## 🚀 使用步骤

### 1. 准备 CSV 数据文件

#### 方法一：使用提供的 SQL 脚本
```bash
# 在 PostgreSQL 中执行数据准备脚本
psql -d your_database -f data_preparation.sql
```

#### 方法二：参考模板手动准备
查看 `csv_templates/` 目录下的示例文件：
- `companies.csv` - 企业节点数据
- `reports.csv` - 报表节点数据
- `persons.csv` - 人员节点数据
- `locations.csv` - 地址节点数据
- `departments.csv` - 部门节点数据
- `company_reports.csv` - 企业-报表关系
- `company_persons.csv` - 企业-人员关系
- `company_locations.csv` - 企业-地址关系
- `report_departments.csv` - 报表-部门关系

### 2. 将 CSV 文件放置到 Neo4j 导入目录
```bash
# 将 CSV 文件复制到 Neo4j 的 import 目录
cp *.csv /var/lib/neo4j/import/
```

### 3. 执行 Neo4j 导入语句
```bash
# 在 Neo4j Browser 或 cypher-shell 中执行
cypher-shell -f neo4j_import_statements.cypher
```

## 📋 字段说明

### 企业节点 (Company)
- `cop_id`: 企业ID（主键）
- `cop_name`: 企业名称
- `cop_code`: 企业编号
- `corporate_code`: 组织机构代码证
- `business_licence`: 营业执照注册号
- `tax_registration`: 税务登记证编号
- `cop_population`: 企业人员规模
- `office_property`: 办公场地属性（0-租用，1-自建，2-转租）
- `is_cancel`: 是否注销（0-未注销，1-已注销）
- `scope_of_business`: 经营范围

### 报表节点 (Report)
- `rep_garrep_id`: 汇总报表ID（主键）
- `rep_date`: 汇总月份
- `level`: 最终风险等级（0-正常，1-问题，2-风险，3-危险，4-高危）
- `level_all`: 各风险等级详情
- `propaymonth`: 拖欠工资月数
- `protaxmonth`: 拖欠税款月数
- `proinsuremonth`: 拖欠社保月数
- `problem`: 存在问题
- `copoversee`: 监管措施
- `copadvice`: 建议措施

### 人员节点 (Person)
- `person_id`: 人员ID（主键）
- `name`: 姓名
- `phone`: 电话
- `person_type`: 人员类型（legal_person-法人，manager-负责人，contact-联系人，office_owner-业主）

## 🔍 查询示例

### 查找高风险企业
```cypher
MATCH (c:Company)-[:HAS_REPORT]->(r:Report)
WHERE r.level >= 3
RETURN c.cop_name, r.level, r.problem
ORDER BY r.level DESC
```

### 查找企业的所有相关人员
```cypher
MATCH (c:Company {cop_name: "示例科技有限公司"})-[:HAS_PERSON]->(p:Person)
RETURN c.cop_name, p.name, p.person_type, p.phone
```

### 分析企业风险趋势
```cypher
MATCH (c:Company)-[:HAS_REPORT]->(r:Report)
WHERE c.cop_id = 1
RETURN r.rep_date, r.level
ORDER BY r.rep_date
```

### 查找同一地区的企业
```cypher
MATCH (c1:Company)-[:LOCATED_AT]->(l:Location)<-[:LOCATED_AT]-(c2:Company)
WHERE c1 <> c2
RETURN l.address, collect(c1.cop_name) as companies
```

## ⚠️ 注意事项

1. **数据清洗**: 执行导入前请确保数据质量，特别是：
   - 日期格式统一为 ISO 8601 格式
   - 数值字段不包含非数字字符
   - 必填字段不为空

2. **性能优化**: 对于大量数据，建议：
   - 分批导入
   - 使用 `USING PERIODIC COMMIT` 语句
   - 在导入完成后创建索引

3. **字符编码**: 确保 CSV 文件使用 UTF-8 编码

4. **路径配置**: 修改 `data_preparation.sql` 中的文件路径为实际路径

## 🛠️ 故障排除

### 常见问题
1. **文件路径错误**: 确保 CSV 文件在 Neo4j 的 import 目录中
2. **数据类型错误**: 检查数值字段是否包含非数字字符
3. **约束冲突**: 确保主键字段唯一性
4. **内存不足**: 对于大数据集，增加 Neo4j 内存配置

### 验证导入结果
```cypher
// 检查节点数量
MATCH (n) RETURN labels(n) as NodeType, count(n) as Count

// 检查关系数量
MATCH ()-[r]->() RETURN type(r) as RelationType, count(r) as Count

// 检查数据完整性
MATCH (c:Company) WHERE NOT (c)-[:HAS_REPORT]->() RETURN count(c) as CompaniesWithoutReports
```

## 📞 支持

如有问题，请检查：
1. Neo4j 日志文件
2. CSV 文件格式和编码
3. 数据类型匹配
4. 约束和索引设置
