-- 数据验证脚本
-- 基于您提供的查询逻辑验证数据完整性

-- ========================================
-- 1. 验证基础数据统计
-- ========================================

-- 您的原始查询：获取有效企业数量
SELECT 
    COUNT(*) as total_valid_companies,
    COUNT(DISTINCT cop_owner) as unique_owners
FROM t_cop_information
WHERE del_flag = '0' 
  AND is_cancel = '0'  
  AND cop_id IN (
      SELECT DISTINCT cop_id 
      FROM t_rep_garrep 
      WHERE del_flag = '0'
  );

-- 验证企业信息表数据质量
SELECT 
    'Company Information Quality Check' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN cop_name IS NOT NULL AND TRIM(cop_name) != '' THEN 1 END) as has_name,
    COUNT(CASE WHEN cop_owner IS NOT NULL AND TRIM(cop_owner) != '' THEN 1 END) as has_owner,
    COUNT(CASE WHEN cop_address IS NOT NULL AND TRIM(cop_address) != '' THEN 1 END) as has_address,
    COUNT(CASE WHEN cop_phone IS NOT NULL AND TRIM(cop_phone) != '' THEN 1 END) as has_phone
FROM t_cop_information
WHERE del_flag = '0' 
  AND is_cancel = '0'  
  AND cop_id IN (
      SELECT DISTINCT cop_id 
      FROM t_rep_garrep 
      WHERE del_flag = '0'
  );

-- 验证报表数据统计
SELECT 
    'Report Data Statistics' as check_type,
    COUNT(*) as total_reports,
    COUNT(DISTINCT cop_id) as companies_with_reports,
    MIN(rep_date) as earliest_report,
    MAX(rep_date) as latest_report,
    COUNT(CASE WHEN level >= 3 THEN 1 END) as high_risk_reports
FROM t_rep_garrep r
INNER JOIN t_cop_information c ON r.cop_id = c.cop_id
WHERE r.del_flag = '0' 
  AND c.del_flag = '0' 
  AND c.is_cancel = '0';

-- ========================================
-- 2. 验证人员数据去重效果
-- ========================================

-- 检查企业法人数据
SELECT 
    'Legal Person Check' as person_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT cop_owner) as unique_names,
    COUNT(CASE WHEN cop_owner IS NOT NULL AND TRIM(cop_owner) != '' THEN 1 END) as valid_names
FROM t_cop_information
WHERE del_flag = '0' 
  AND is_cancel = '0'  
  AND cop_id IN (
      SELECT DISTINCT cop_id 
      FROM t_rep_garrep 
      WHERE del_flag = '0'
  );

-- 检查重复人员情况
WITH person_analysis AS (
    SELECT 
        cop_id,
        cop_owner,
        cop_incharge,
        cop_contacts,
        office_owner,
        CASE WHEN cop_owner = cop_incharge THEN 1 ELSE 0 END as owner_is_manager,
        CASE WHEN cop_owner = cop_contacts THEN 1 ELSE 0 END as owner_is_contact,
        CASE WHEN cop_owner = office_owner THEN 1 ELSE 0 END as owner_is_office_owner
    FROM t_cop_information
    WHERE del_flag = '0' 
      AND is_cancel = '0'  
      AND cop_id IN (
          SELECT DISTINCT cop_id 
          FROM t_rep_garrep 
          WHERE del_flag = '0'
      )
)
SELECT 
    'Person Role Overlap Analysis' as analysis_type,
    SUM(owner_is_manager) as owner_also_manager,
    SUM(owner_is_contact) as owner_also_contact,
    SUM(owner_is_office_owner) as owner_also_office_owner,
    COUNT(*) as total_companies
FROM person_analysis;

-- ========================================
-- 3. 验证地理位置数据
-- ========================================

-- 检查地理坐标数据质量
SELECT 
    'Geographic Data Quality' as check_type,
    COUNT(*) as total_companies,
    COUNT(CASE WHEN lon IS NOT NULL AND lon ~ '^[0-9]+\.?[0-9]*$' THEN 1 END) as valid_longitude,
    COUNT(CASE WHEN lat IS NOT NULL AND lat ~ '^[0-9]+\.?[0-9]*$' THEN 1 END) as valid_latitude,
    COUNT(CASE WHEN lon IS NOT NULL AND lon ~ '^[0-9]+\.?[0-9]*$' 
                AND lat IS NOT NULL AND lat ~ '^[0-9]+\.?[0-9]*$' THEN 1 END) as complete_coordinates
FROM t_cop_information
WHERE del_flag = '0' 
  AND is_cancel = '0'  
  AND cop_id IN (
      SELECT DISTINCT cop_id 
      FROM t_rep_garrep 
      WHERE del_flag = '0'
  );

-- ========================================
-- 4. 验证风险等级分布
-- ========================================

-- 风险等级分布统计
SELECT 
    'Risk Level Distribution' as analysis_type,
    COALESCE(level, 0) as risk_level,
    CASE 
        WHEN COALESCE(level, 0) = 0 THEN '正常'
        WHEN level = 1 THEN '问题'
        WHEN level = 2 THEN '风险'
        WHEN level = 3 THEN '危险'
        WHEN level = 4 THEN '高危'
        ELSE '未知'
    END as risk_description,
    COUNT(*) as report_count,
    COUNT(DISTINCT cop_id) as company_count
FROM t_rep_garrep r
INNER JOIN t_cop_information c ON r.cop_id = c.cop_id
WHERE r.del_flag = '0' 
  AND c.del_flag = '0' 
  AND c.is_cancel = '0'
GROUP BY COALESCE(level, 0)
ORDER BY risk_level;

-- ========================================
-- 5. 验证拖欠情况统计
-- ========================================

-- 拖欠情况汇总
SELECT 
    'Arrears Summary' as analysis_type,
    COUNT(CASE WHEN COALESCE(propaymonth, 0) > 0 THEN 1 END) as salary_arrears_count,
    COUNT(CASE WHEN COALESCE(protaxmonth, 0) > 0 THEN 1 END) as tax_arrears_count,
    COUNT(CASE WHEN COALESCE(proinsuremonth, 0) > 0 THEN 1 END) as insurance_arrears_count,
    COUNT(CASE WHEN COALESCE(prowatermonth, 0) > 0 THEN 1 END) as water_arrears_count,
    COUNT(CASE WHEN COALESCE(proenergymonth, 0) > 0 THEN 1 END) as energy_arrears_count,
    COUNT(CASE WHEN COALESCE(prorentmonth, 0) > 0 THEN 1 END) as rent_arrears_count,
    AVG(COALESCE(propaymonth, 0)) as avg_salary_arrears_months,
    AVG(COALESCE(protaxmonth, 0)) as avg_tax_arrears_months
FROM t_rep_garrep r
INNER JOIN t_cop_information c ON r.cop_id = c.cop_id
WHERE r.del_flag = '0' 
  AND c.del_flag = '0' 
  AND c.is_cancel = '0';

-- ========================================
-- 6. 验证部门数据
-- ========================================

-- 部门统计
SELECT 
    'Department Statistics' as analysis_type,
    COUNT(DISTINCT dept_id) as unique_departments,
    COUNT(*) as total_reports_with_dept,
    dept_id,
    COUNT(*) as reports_per_dept
FROM t_rep_garrep r
INNER JOIN t_cop_information c ON r.cop_id = c.cop_id
WHERE r.dept_id IS NOT NULL 
  AND r.del_flag = '0' 
  AND c.del_flag = '0' 
  AND c.is_cancel = '0'
GROUP BY dept_id
ORDER BY reports_per_dept DESC;

-- ========================================
-- 7. 最终数据完整性检查
-- ========================================

-- 数据关联完整性检查
SELECT 
    'Data Integrity Check' as check_type,
    (SELECT COUNT(*) FROM t_cop_information 
     WHERE del_flag = '0' AND is_cancel = '0' 
       AND cop_id IN (SELECT DISTINCT cop_id FROM t_rep_garrep WHERE del_flag = '0')) as valid_companies,
    (SELECT COUNT(*) FROM t_rep_garrep r 
     INNER JOIN t_cop_information c ON r.cop_id = c.cop_id 
     WHERE r.del_flag = '0' AND c.del_flag = '0' AND c.is_cancel = '0') as valid_reports,
    (SELECT COUNT(DISTINCT r.cop_id) FROM t_rep_garrep r 
     INNER JOIN t_cop_information c ON r.cop_id = c.cop_id 
     WHERE r.del_flag = '0' AND c.del_flag = '0' AND c.is_cancel = '0') as companies_with_reports;
