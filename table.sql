create table t_cop_information
(
    cop_id             bigint not null
        constraint t_cop_information_pk
            primary key,
    create_time        timestamp,
    del_flag           bpchar  default '0'::bpchar,
    note               varchar,
    cop_name           varchar(50),
    cop_code           varchar(50),
    cop_owner          varchar(50),
    cop_incharge       varchar(50),
    corporate_code     varchar(50),
    business_licence   varchar(50),
    tax_registration   varchar(50),
    cop_population     integer default 0,
    cop_address        varchar(100),
    cop_phone          varchar(20),
    cop_fax            varchar(20),
    office_property    integer default 0,
    office_owner       varchar(50),
    office_owner_phone varchar(50),
    property_id        integer,
    org_id             bigint,
    lon                varchar(50),
    lat                varchar(50),
    is_cancel          integer default 0,
    scope_of_business  varchar,
    cop_contacts       varchar(50),
    lease_term         varchar(50),
    by1                varchar,
    by2                varchar,
    update_time        timestamp,
    create_by          varchar(64),
    update_by          varchar(64),
    geom               varchar(100),
    is_location        integer default 0,
    is_local           integer default 1
);

comment on table t_cop_information is '企业信息表';

comment on column t_cop_information.cop_id is '企业ID';

comment on column t_cop_information.create_time is '创建时间';

comment on column t_cop_information.del_flag is '是否删除';

comment on column t_cop_information.note is '备注';

comment on column t_cop_information.cop_name is '企业名称';

comment on column t_cop_information.cop_code is '企业编号';

comment on column t_cop_information.cop_owner is '企业法人';

comment on column t_cop_information.cop_incharge is '企业负责人';

comment on column t_cop_information.corporate_code is '组织机构代码证';

comment on column t_cop_information.business_licence is '营业执照注册号';

comment on column t_cop_information.tax_registration is '税务登记证编号';

comment on column t_cop_information.cop_population is '企业人员规模';

comment on column t_cop_information.cop_address is '企业地址';

comment on column t_cop_information.cop_phone is '企业电话';

comment on column t_cop_information.cop_fax is '企业传真';

comment on column t_cop_information.office_property is '办公场地属性（默认0，0——租用、1——自建、2——转租）';

comment on column t_cop_information.office_owner is '厂房业主';

comment on column t_cop_information.office_owner_phone is '厂房业主电话';

comment on column t_cop_information.property_id is '企业性质ID';

comment on column t_cop_information.org_id is '机构ID';

comment on column t_cop_information.lon is '经度';

comment on column t_cop_information.lat is '纬度';

comment on column t_cop_information.is_cancel is '是否注销（默认0，0——未注销、1——已注销）';

comment on column t_cop_information.scope_of_business is '经营范围';

comment on column t_cop_information.cop_contacts is '企业联系人';

comment on column t_cop_information.lease_term is '厂房租赁期限';

comment on column t_cop_information.by1 is '备用字段1';

comment on column t_cop_information.by2 is '备用字段2';

comment on column t_cop_information.is_location is '是否定位 0否 1是';

comment on column t_cop_information.is_local is '是否本镇企业';

alter table t_cop_information
    owner to postgis;

create table t_rep_garrep
(
    rep_garrep_id    bigint not null,
    create_time      timestamp,
    del_flag         bpchar  default '0'::bpchar,
    note             varchar,
    rep_date         timestamp,
    cop_id           bigint,
    level_all        varchar(100),
    level            integer default 0,
    propaymonth      numeric(18, 2),
    propaypopulation numeric(18, 2),
    propaymoney      numeric(18, 2),
    protaxmonth      numeric(18, 2),
    protaxmoney      numeric(18, 2),
    proinsuremonth   numeric(18, 2),
    proinsuremoney   numeric(18, 2),
    prowatermonth    numeric(18, 2),
    prowatermoney    numeric(18, 2),
    proenergymonth   numeric(18, 2),
    proenergymoney   numeric(18, 2),
    prorentmonth     numeric(18, 2),
    prorentmoney     numeric(18, 2),
    problem          varchar(4000),
    copoversee       varchar(4000),
    copadvice        varchar(4000),
    copdeal          varchar(4000),
    copresult        varchar(4000),
    copafterwork     varchar(4000),
    cophelp          varchar(4000),
    update_time      timestamp,
    create_by        varchar(64),
    update_by        varchar(64),
    dept_id          bigint,
    other            varchar(2000),
    propaymonths     varchar(255),
    protaxmonths     varchar(255),
    proinsuremonths  varchar(255),
    prowatermonths   varchar(255),
    proenergymonths  varchar(255),
    prorentmonths    varchar(255)
);

comment on table t_rep_garrep is '汇总报表，指标字段根据动态添加';

comment on column t_rep_garrep.rep_garrep_id is '汇总报表ID';

comment on column t_rep_garrep.create_time is '创建时间';

comment on column t_rep_garrep.del_flag is '是否删除';

comment on column t_rep_garrep.note is '备注';

comment on column t_rep_garrep.rep_date is '汇总月份';

comment on column t_rep_garrep.cop_id is '企业ID';

comment on column t_rep_garrep.level_all is '各风险等级（逗号分隔，如1,1,2,4）';

comment on column t_rep_garrep.level is '最终风险等级（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）';

comment on column t_rep_garrep.copoversee is '监管措施';

comment on column t_rep_garrep.copadvice is '建议措施';

comment on column t_rep_garrep.copdeal is '采取措施';

comment on column t_rep_garrep.copresult is '企业现状';

comment on column t_rep_garrep.copafterwork is '后续工作';

comment on column t_rep_garrep.cophelp is '请求协助';

comment on column t_rep_garrep.dept_id is '上报部门ID';

comment on column t_rep_garrep.propaymonths is '拖欠工资（月）';

comment on column t_rep_garrep.protaxmonths is '拖欠税款（月）';

comment on column t_rep_garrep.proinsuremonths is '拖欠社保（月）';

comment on column t_rep_garrep.prowatermonths is '拖欠水费（月）';

comment on column t_rep_garrep.proenergymonths is '拖欠电费（月）';

comment on column t_rep_garrep.prorentmonths is '拖欠租金（月）';

alter table t_rep_garrep
    owner to postgis;

