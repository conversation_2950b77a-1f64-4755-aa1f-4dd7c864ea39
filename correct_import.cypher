-- 正确的企业-法人关系导入语句

-- 1. 创建约束
CREATE CONSTRAINT company_id_unique IF NOT EXISTS FOR (c:Company) REQUIRE c.cop_id IS UNIQUE;
CREATE CONSTRAINT legal_person_name_unique IF NOT EXISTS FOR (p:LegalPerson) REQUIRE p.cop_owner IS UNIQUE;

-- 2. 导入企业和法人关系（正确语法）
LOAD CSV WITH HEADERS FROM "file:///company.csv" AS line
WITH line WHERE line.cop_owner IS NOT NULL AND trim(line.cop_owner) <> ""
MERGE (c:Company {cop_id: toInteger(line.cop_id)})
SET c.cop_name = line.cop_name,
    c.cop_owner = line.cop_owner,
    c.cop_code = line.cop_code,
    c.cop_address = line.cop_address,
    c.cop_phone = line.cop_phone
WITH line, c
MERGE (p:LegalPerson {cop_owner: line.cop_owner})
SET p.phone = line.cop_phone
WITH line, c, p
MERGE (c)-[:HAS_LEGAL_PERSON]->(p);

-- 3. 验证结果
MATCH (c:Company) RETURN count(c) as companies;
MATCH (p:LegalPerson) RETURN count(p) as legal_persons;
MATCH ()-[r:HAS_LEGAL_PERSON]->() RETURN count(r) as relations;
