-- 直接从企业表导入企业和法人关系的Neo4j语句

-- ========================================
-- 方法1：分步导入（推荐）
-- ========================================

-- 第1步：创建约束
CREATE CONSTRAINT company_id_unique IF NOT EXISTS FOR (c:Company) REQUIRE c.cop_id IS UNIQUE;
CREATE CONSTRAINT legal_person_name_unique IF NOT EXISTS FOR (p:LegalPerson) REQUIRE p.cop_owner IS UNIQUE;

-- 第2步：导入企业节点
LOAD CSV WITH HEADERS FROM "file:///company.csv" AS line
MERGE (c:Company {cop_id: toInteger(line.cop_id)})
SET c.cop_name = line.cop_name,
    c.cop_code = line.cop_code,
    c.cop_owner = line.cop_owner,
    c.corporate_code = line.corporate_code,
    c.business_licence = line.business_licence,
    c.cop_address = line.cop_address,
    c.cop_phone = line.cop_phone,
    c.scope_of_business = line.scope_of_business;

-- 第3步：导入法人节点
LOAD CSV WITH HEADERS FROM "file:///company.csv" AS line
WHERE line.cop_owner IS NOT NULL AND trim(line.cop_owner) <> ""
MERGE (p:LegalPerson {cop_owner: line.cop_owner})
SET p.phone = line.cop_phone;

-- 第4步：创建企业-法人关系
LOAD CSV WITH HEADERS FROM "file:///company.csv" AS line
WHERE line.cop_owner IS NOT NULL AND trim(line.cop_owner) <> ""
MATCH (c:Company {cop_id: toInteger(line.cop_id)})
MATCH (p:LegalPerson {cop_owner: line.cop_owner})
MERGE (c)-[:HAS_LEGAL_PERSON]->(p);

-- ========================================
-- 方法2：一步导入（修正版）
-- ========================================

-- 创建约束（如果还没创建）
CREATE CONSTRAINT company_id_unique IF NOT EXISTS FOR (c:Company) REQUIRE c.cop_id IS UNIQUE;
CREATE CONSTRAINT legal_person_name_unique IF NOT EXISTS FOR (p:LegalPerson) REQUIRE p.cop_owner IS UNIQUE;

-- 一步完成导入
LOAD CSV WITH HEADERS FROM "file:///company.csv" AS line
WHERE line.cop_owner IS NOT NULL AND trim(line.cop_owner) <> ""
MERGE (c:Company {cop_id: toInteger(line.cop_id)})
SET c.cop_name = line.cop_name,
    c.cop_code = line.cop_code,
    c.cop_owner = line.cop_owner,
    c.corporate_code = line.corporate_code,
    c.business_licence = line.business_licence,
    c.cop_address = line.cop_address,
    c.cop_phone = line.cop_phone,
    c.scope_of_business = line.scope_of_business
WITH line, c
MERGE (p:LegalPerson {cop_owner: line.cop_owner})
SET p.phone = line.cop_phone
WITH line, c, p
MERGE (c)-[:HAS_LEGAL_PERSON]->(p);

-- ========================================
-- 验证导入结果
-- ========================================

// 检查节点数量
MATCH (c:Company) RETURN 'Company' as NodeType, count(c) as Count
UNION ALL
MATCH (p:LegalPerson) RETURN 'LegalPerson' as NodeType, count(p) as Count;

// 检查关系数量
MATCH (c:Company)-[r:HAS_LEGAL_PERSON]->(p:LegalPerson) 
RETURN 'HAS_LEGAL_PERSON' as RelationType, count(r) as Count;

// 查看示例数据
MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson) 
RETURN c.cop_name, p.cop_owner, c.cop_phone 
LIMIT 10;
