# 企业-法人关系知识图谱简化版

基于您的查询逻辑，只包含企业和法人的关系导入。

## 📊 数据模型

### 实体类型
- **Company (企业)**: 企业基本信息
- **LegalPerson (法人)**: 企业法人信息

### 关系类型
- **HAS_LEGAL_PERSON**: 企业 → 法人

## 🚀 使用步骤

### 1. 生成 CSV 数据文件
```bash
# 在 PostgreSQL 中执行数据准备脚本
psql -d your_database -f company_legal_person_simple.sql
```

### 2. 将 CSV 文件放置到 Neo4j 导入目录
```bash
# 将 CSV 文件复制到 Neo4j 的 import 目录
cp companies.csv legal_persons.csv company_legal_person_relations.csv /var/lib/neo4j/import/
```

### 3. 执行 Neo4j 导入语句
```bash
# 在 Neo4j Browser 或 cypher-shell 中执行
cypher-shell -f neo4j_import_simple.cypher
```

## 📋 生成的文件

1. **companies.csv** - 企业节点数据
   - cop_id, cop_name, cop_code, cop_owner, corporate_code, business_licence 等

2. **legal_persons.csv** - 法人节点数据
   - person_name, phone

3. **company_legal_person_relations.csv** - 企业-法人关系数据
   - cop_id, person_name, relation_create_time

## 🔍 查询示例

### 查找企业的法人
```cypher
MATCH (c:Company {cop_name: "示例科技有限公司"})-[:HAS_LEGAL_PERSON]->(p:LegalPerson)
RETURN c.cop_name, p.person_name, p.phone
```

### 查找法人名下的所有企业
```cypher
MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson {person_name: "张三"})
RETURN p.person_name, collect(c.cop_name) as companies
```

### 统计法人企业数量
```cypher
MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson)
RETURN p.person_name, count(c) as company_count
ORDER BY company_count DESC
```

### 查找同一法人的多个企业
```cypher
MATCH (c:Company)-[:HAS_LEGAL_PERSON]->(p:LegalPerson)
WITH p, collect(c) as companies
WHERE size(companies) > 1
RETURN p.person_name, [c IN companies | c.cop_name] as company_names
```

## ⚠️ 注意事项

1. **数据过滤**: 基于您的查询逻辑 `del_flag = '0' AND is_cancel = '0' AND cop_id IN (SELECT cop_id FROM t_rep_garrep)`
2. **法人去重**: 自动按法人姓名去重
3. **路径配置**: 修改 SQL 脚本中的 `/path/to/csv/` 为实际路径
4. **字符编码**: 确保 CSV 文件使用 UTF-8 编码

## 📞 验证导入

```cypher
// 检查数据统计
MATCH (c:Company) RETURN count(c) as total_companies;
MATCH (p:LegalPerson) RETURN count(p) as total_legal_persons;
MATCH ()-[r:HAS_LEGAL_PERSON]->() RETURN count(r) as total_relations;

// 检查数据完整性
MATCH (c:Company) WHERE NOT (c)-[:HAS_LEGAL_PERSON]->() 
RETURN count(c) as companies_without_legal_person;
```
